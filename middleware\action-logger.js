const db = require('../config/db');

/**
 * 操作日志记录中间件
 * 记录用户的API操作行为
 */
const actionLogger = (req, res, next) => {
    // 保存原始的res.json方法
    const originalJson = res.json;
    
    // 重写res.json方法以捕获响应
    res.json = function(data) {
        // 只记录成功的API调用和认证过的用户操作
        if (res.statusCode >= 200 && res.statusCode < 300 && req.user) {
            // 异步记录日志，不阻塞响应
            setImmediate(() => {
                logAction(req, res, data);
            });
        }
        
        // 调用原始的json方法
        return originalJson.call(this, data);
    };
    
    next();
};

/**
 * 记录操作日志
 */
async function logAction(req, res, responseData) {
    try {
        const { method, originalUrl, user, ip, body, params, query } = req;
        
        // 从URL中解析目标表名
        const urlParts = originalUrl.split('?')[0].split('/');
        let targetTable = null;
        
        // 解析API路径，提取表名
        if (urlParts.length > 2 && urlParts[1] === 'api') {
            targetTable = urlParts[2];
            
            // 处理特殊路径
            if (targetTable === 'raw-table-data') {
                targetTable = 'raw_table_data';
            } else if (targetTable === 'store-feedback') {
                targetTable = 'store_feedback';
            } else if (targetTable === 'action-logs') {
                // 不记录查看日志的操作
                return;
            }
        }
        
        // 如果无法确定目标表，跳过记录
        if (!targetTable) {
            return;
        }
        
        // 映射HTTP方法到操作类型
        const actionMap = {
            'GET': 'READ',
            'POST': 'create',
            'PUT': 'update',
            'PATCH': 'update',
            'DELETE': 'delete'
        };
        const actionType = actionMap[method] || method.toLowerCase();
        
        // 获取目标ID
        const targetId = params.id || null;
        
        // 构建详细信息
        let details = {};
        
        // 对于创建和更新操作，记录请求体
        if (['POST', 'PUT', 'PATCH'].includes(method) && body && Object.keys(body).length > 0) {
            // 过滤敏感信息
            const { password, token, image, ...safeBody } = body;
            if (Object.keys(safeBody).length > 0) {
                details.body = safeBody;
            }
            if (image) {
                details.hasImage = true;
            }
        }
        
        // 对于读取操作，记录查询参数
        if (method === 'GET' && query && Object.keys(query).length > 0) {
            details.query = query;
        }
        
        // 记录响应信息（仅记录关键信息）
        if (responseData && typeof responseData === 'object') {
            if (responseData.message) {
                details.message = responseData.message;
            }
            if (responseData.total !== undefined) {
                details.total = responseData.total;
            }
        }
        
        const finalDetails = Object.keys(details).length > 0 ? JSON.stringify(details) : null;
        
        // 插入日志记录
        await db.query(`
            INSERT INTO action_logs (user_id, username, action_type, target_table, target_id, details, ip_address)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
            user.id,
            user.username,
            actionType,
            targetTable,
            targetId,
            finalDetails,
            ip || req.connection.remoteAddress
        ]);
        
    } catch (error) {
        console.error('记录操作日志失败:', error);
        // 日志记录失败不应该影响正常业务流程
    }
}

module.exports = actionLogger;
