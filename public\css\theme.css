/* 主题切换样式 */

/* 确保Tailwind的dark模式正常工作 */
@media (prefers-color-scheme: dark) {
  /* 这里可以添加系统偏好的暗色主题样式，但我们主要使用手动切换 */
}

/* 主题切换按钮的过渡效果 */
.theme-transition {
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

/* 自定义滚动条样式 */
.dark ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.dark ::-webkit-scrollbar-track {
    background: #374151;
}

.dark ::-webkit-scrollbar-thumb {
    background: #6B7280;
    border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: #9CA3AF;
}

/* 亮色主题滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #F3F4F6;
}

::-webkit-scrollbar-thumb {
    background: #D1D5DB;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #9CA3AF;
}

/* 表格行的悬停效果增强 */
.dark tbody tr:hover {
    background-color: rgba(55, 65, 81, 0.5);
}

tbody tr:hover {
    background-color: rgba(249, 250, 251, 0.8);
}

/* 输入框焦点状态增强 */
.dark input:focus,
.dark select:focus,
.dark textarea:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 按钮悬停效果增强 */
.dark button:hover {
    transform: translateY(-1px);
    transition: transform 0.1s ease-in-out;
}

button:hover {
    transform: translateY(-1px);
    transition: transform 0.1s ease-in-out;
}

/* 模态框背景 */
.dark .modal-backdrop {
    background-color: rgba(0, 0, 0, 0.7);
}

.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.5);
}

/* Toast通知增强 */
.dark #toast {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

#toast {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 卡片阴影增强 */
.dark .card-shadow {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.card-shadow {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 主题切换按钮特殊样式 */
#themeToggle {
    position: relative;
    overflow: hidden;
}

#themeToggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#themeToggle:hover::before {
    left: 100%;
}

/* 深色主题下的主题切换按钮 */
.dark #themeToggle::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

/* 表格边框增强 */
.dark table {
    border-color: #374151;
}

table {
    border-color: #E5E7EB;
}

/* 分页按钮增强 */
.dark .pagination-btn:disabled {
    background-color: #374151;
    color: #6B7280;
}

.pagination-btn:disabled {
    background-color: #F3F4F6;
    color: #9CA3AF;
}

/* 搜索框图标颜色 */
.dark .search-icon {
    color: #6B7280;
}

.search-icon {
    color: #9CA3AF;
}

/* 角色标签增强 */
.role-badge-admin {
    background: linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%);
    color: #991B1B;
}

.dark .role-badge-admin {
    background: linear-gradient(135deg, #7F1D1D 0%, #991B1B 100%);
    color: #FCA5A5;
}

.role-badge-user {
    background: linear-gradient(135deg, #DBEAFE 0%, #BFDBFE 100%);
    color: #1E40AF;
}

.dark .role-badge-user {
    background: linear-gradient(135deg, #1E3A8A 0%, #1E40AF 100%);
    color: #93C5FD;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* 加载动画增强 */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

/* 响应式设计增强 */
@media (max-width: 640px) {
    .mobile-stack {
        flex-direction: column;
    }
    
    .mobile-full-width {
        width: 100%;
    }
    
    .mobile-w-full {
        width: 100%;
    }
    
    .mobile-mt-2 {
        margin-top: 0.5rem;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .dark {
        --tw-bg-opacity: 1;
        background-color: rgb(0 0 0 / var(--tw-bg-opacity));
        color: rgb(255 255 255);
    }
    
    .dark input,
    .dark select,
    .dark textarea {
        border-width: 2px;
        border-color: rgb(255 255 255);
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
