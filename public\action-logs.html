<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志 - 电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- 外部样式 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 自定义样式 -->
    <link rel="stylesheet" href="/css/action-logs.css">

    <!-- 移除Particles.js -->
</head>
<body>
    <div class="action-logs-container">

        <!-- 页面头部 -->
        <header class="page-header">
            <div class="container mx-auto px-4">
                <div class="flex justify-between items-center">
                    <h1 class="page-title">
                        <i class="fas fa-history"></i>
                        操作日志
                    </h1>
                    <div class="flex items-center gap-4">
                        <span id="userInfo" class="text-sm text-gray-600">
                            <i class="fas fa-user"></i>
                            <span id="currentUser">加载中...</span>
                        </span>
                        <button id="themeToggle" class="btn btn-secondary theme-toggle" title="单击切换主题 (Ctrl+T) | 双击切换暗色主题 (Ctrl+Shift+D)">
                            <i class="fas fa-palette"></i>
                            <span id="themeText">默认主题</span>
                        </button>
                        <a href="/pc-components.html" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            返回首页
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="container mx-auto px-4">
            <!-- 筛选器区域 -->
            <section class="filters-section">
                <h2 class="text-lg font-semibold mb-4 text-gray-800">
                    <i class="fas fa-filter"></i>
                    筛选条件
                </h2>
                
                <div class="filters-grid">
                    <div class="filter-group">
                        <label class="filter-label" for="usernameFilter">用户名</label>
                        <input type="text" id="usernameFilter" class="filter-input" placeholder="输入用户名">
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label" for="actionTypeFilter">操作类型</label>
                        <select id="actionTypeFilter" class="filter-select">
                            <option value="all">全部</option>
                            <option value="create">创建</option>
                            <option value="read">读取</option>
                            <option value="update">更新</option>
                            <option value="delete">删除</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label" for="targetTableFilter">目标表</label>
                        <select id="targetTableFilter" class="filter-select">
                            <option value="all">全部</option>
                            <option value="cpus">CPU</option>
                            <option value="gpus">显卡</option>
                            <option value="motherboards">主板</option>
                            <option value="rams">内存</option>
                            <option value="storages">存储</option>
                            <option value="psus">电源</option>
                            <option value="coolers">散热器</option>
                            <option value="fans">风扇</option>
                            <option value="cases">机箱</option>
                            <option value="monitors">显示器</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label" for="startDateFilter">开始日期</label>
                        <input type="date" id="startDateFilter" class="filter-input">
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label" for="endDateFilter">结束日期</label>
                        <input type="date" id="endDateFilter" class="filter-input">
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label" for="globalSearch">全局搜索</label>
                        <input type="text" id="globalSearch" class="filter-input" placeholder="搜索用户名、表名、详情...">
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button id="searchBtn" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                    <button id="clearFiltersBtn" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        清空筛选
                    </button>
                    <button id="refreshBtn" class="btn btn-secondary">
                        <i class="fas fa-sync-alt"></i>
                        刷新
                    </button>
                    <button id="exportBtn" class="btn btn-secondary">
                        <i class="fas fa-download"></i>
                        导出
                    </button>
                    <button id="cleanupBtn" class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        清理旧日志
                    </button>
                </div>
            </section>

            <!-- 统计信息 -->
            <section id="statsSection" class="filters-section" style="display: none;">
                <h2 class="text-lg font-semibold mb-4 text-gray-800">
                    <i class="fas fa-chart-bar"></i>
                    统计信息
                </h2>
                <div id="statsContent" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- 统计内容将通过JavaScript动态加载 -->
                </div>
            </section>

            <!-- 日志表格 -->
            <section class="table-container">
                <div class="overflow-x-auto">
                    <table class="logs-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户</th>
                                <th>操作类型</th>
                                <th>目标表</th>
                                <th>目标ID</th>
                                <th>IP地址</th>
                                <th>操作时间</th>
                                <th>详情</th>
                            </tr>
                        </thead>
                        <tbody id="logsTableBody">
                            <tr>
                                <td colspan="8" class="loading">
                                    <i class="fas fa-spinner"></i>
                                    正在加载数据...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="pagination">
                    <div class="pagination-info">
                        显示第 <span id="pageStart">0</span> - <span id="pageEnd">0</span> 条，
                        共 <span id="totalCount">0</span> 条记录
                    </div>
                    <div class="pagination-controls">
                        <button id="prevPageBtn" class="btn btn-secondary" disabled>
                            <i class="fas fa-chevron-left"></i>
                            上一页
                        </button>
                        <span id="pageInfo" class="px-3 py-2 text-sm text-gray-600">
                            第 <span id="currentPage">1</span> / <span id="totalPages">1</span> 页
                        </span>
                        <button id="nextPageBtn" class="btn btn-secondary" disabled>
                            下一页
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 通知容器 -->
    <div id="notificationContainer"></div>

    <!-- 主题状态指示器 -->
    <div id="themeIndicator" class="theme-indicator">
        <i class="fas fa-palette"></i>
        <span id="themeIndicatorText">默认主题</span>
    </div>

    <!-- 清理确认模态框 -->
    <div id="cleanupModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>清理旧日志</h3>
            <p>请选择要清理多少天前的日志记录：</p>
            <div class="form-group">
                <label for="cleanupDays">保留天数：</label>
                <input type="number" id="cleanupDays" value="30" min="1" max="365">
            </div>
            <div class="modal-actions">
                <button id="confirmCleanup" class="btn btn-danger">确认清理</button>
                <button id="cancelCleanup" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="/js/action-logs.js"></script>
</body>
</html>
