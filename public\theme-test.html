<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题切换测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 主题管理器 -->
    <script src="/js/theme-manager.js"></script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-200">
    <div class="container mx-auto px-4 py-8">
        <header class="mb-8">
            <div class="flex justify-between items-center">
                <h1 class="text-3xl font-bold text-gray-800 dark:text-gray-200">主题切换测试</h1>
                
                <!-- 主题切换按钮 -->
                <button data-theme-toggle 
                        class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
                        title="切换主题">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </header>
        
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md transition-colors duration-200">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">测试内容</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-4">这是一个测试页面，用于验证主题切换功能是否正常工作。</p>
            
            <div class="space-y-4">
                <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">表单元素测试</h3>
                    <input type="text" placeholder="输入测试文本..." 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md">
                </div>
                
                <div class="flex space-x-2">
                    <button class="px-4 py-2 bg-indigo-600 dark:bg-indigo-500 text-white rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-600 transition-colors duration-200">
                        主要按钮
                    </button>
                    <button class="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors duration-200">
                        次要按钮
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面加载完成');
            console.log('主题管理器状态:', window.themeManager ? '已加载' : '未加载');
            
            if (window.themeManager) {
                console.log('当前主题:', window.themeManager.getCurrentTheme());
                
                // 监听主题变化事件
                document.addEventListener('themechange', function(e) {
                    console.log('主题已切换到:', e.detail.theme);
                });
            }
        });
    </script>
</body>
</html>
